package com.yf.exam.modules.weather.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.core.api.controller.BaseController;
import com.yf.exam.modules.user.UserUtils;
import com.yf.exam.modules.weather.config.WeatherUploadConfig;
import com.yf.exam.modules.weather.utils.GeometryUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.UUID;

/**
 * 历史个例文件上传控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@RestController
@RequestMapping("/exam/api/weather/case")
@Api(tags = "历史个例文件上传")
public class WeatherFileUploadController extends BaseController {

    @Autowired
    private WeatherUploadConfig weatherUploadConfig;

    /**
     * 动态生成访问URL
     */
    private String generateAccessUrl(HttpServletRequest request, String relativePath) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();
        String contextPath = request.getContextPath();

        StringBuilder url = new StringBuilder();
        url.append(scheme).append("://").append(serverName);

        // 只有在非标准端口时才添加端口号
        if ((scheme.equals("http") && serverPort != 80) ||
            (scheme.equals("https") && serverPort != 443)) {
            url.append(":").append(serverPort);
        }

        url.append(contextPath);
        if (!contextPath.endsWith("/")) {
            url.append("/");
        }
        url.append("upload/file/weather/").append(relativePath);

        return url.toString();
    }

    /**
     * 上传MICAPS文件
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传MICAPS文件")
    @PostMapping("/upload/micaps")
    public ApiRest<Map<String, Object>> uploadMicapsFile(@RequestParam("file") MultipartFile file,
                                                        HttpServletRequest request) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为300MB）
            long maxSize = 300 * 1024 * 1024; // 300MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过300MB");
            }

            // 创建上传目录
            String uploadDir = weatherUploadConfig.getMicapsDir();
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                log.info("创建MICAPS上传目录: {} - {}", uploadDir, created ? "成功" : "失败");
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 验证文件是否保存成功
            File savedFile = new File(filePath);
            if (!savedFile.exists()) {
                log.error("文件保存失败，路径: {}", filePath);
                return super.failure("文件保存失败");
            }

            log.info("MICAPS文件保存成功: {} -> {}", originalFilename, filePath);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            // 动态生成访问URL
            result.put("url", generateAccessUrl(request, "micaps/" + fileName));
            // 返回绝对路径，便于后续读取
            result.put("filePath", filePath);
            // 同时返回相对路径，保持兼容性
            result.put("relativePath", "weather/micaps/" + fileName);

            log.info("用户 {} 上传了MICAPS文件：{} -> {}",
                UserUtils.getUser().getUserName(), originalFilename, filePath);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传MICAPS文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传MICAPS文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 上传历史个例实况文件
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传历史个例实况文件")
    @PostMapping("/upload/observation")
    public ApiRest<Map<String, Object>> uploadObservationFile(@RequestParam("file") MultipartFile file,
                                                              HttpServletRequest request) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为50MB）
            long maxSize = 50 * 1024 * 1024; // 50MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过50MB");
            }

            // 创建上传目录
            String uploadDir = weatherUploadConfig.getObservationDir();
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                log.info("创建实况文件上传目录: {} - {}", uploadDir, created ? "成功" : "失败");
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 验证文件是否保存成功
            File savedFile = new File(filePath);
            if (!savedFile.exists()) {
                log.error("实况文件保存失败，路径: {}", filePath);
                return super.failure("文件保存失败");
            }

            log.info("实况文件保存成功: {} -> {}", originalFilename, filePath);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            // 动态生成访问URL
            result.put("url", generateAccessUrl(request, "observation/" + fileName));
            // 返回绝对路径，便于后续读取
            result.put("filePath", filePath);
            // 同时返回相对路径，保持兼容性
            result.put("relativePath", "weather/observation/" + fileName);

            log.info("用户 {} 上传了实况文件：{} -> {}",
                UserUtils.getUser().getUserName(), originalFilename, filePath);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传实况文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传实况文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 上传历史个例数据文件（压缩包）
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "上传历史个例数据文件")
    @PostMapping("/upload/data")
    public ApiRest<Map<String, Object>> uploadDataFile(@RequestParam("file") MultipartFile file,
                                                       HttpServletRequest request) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }

            // 验证文件大小（限制为500MB）
            long maxSize = 500 * 1024 * 1024; // 500MB
            if (file.getSize() > maxSize) {
                return super.failure("文件大小不能超过500MB");
            }

            // 验证文件类型（压缩文件）
            String extension = getFileExtension(originalFilename);
            if (!isArchiveFile(extension)) {
                return super.failure("不支持的文件格式，支持格式：.zip, .rar, .7z, .tar, .gz, .bz2");
            }

            // 创建上传目录
            String uploadDir = weatherUploadConfig.getDataDir();
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                log.info("创建数据文件上传目录: {} - {}", uploadDir, created ? "成功" : "失败");
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            // 动态生成访问URL
            result.put("url", generateAccessUrl(request, "data/" + fileName));
            // 返回绝对路径，便于后续读取
            result.put("filePath", filePath);
            // 同时返回相对路径，保持兼容性
            result.put("relativePath", "weather/data/" + fileName);

            log.info("用户 {} 上传了数据文件：{}", 
                UserUtils.getUser().getUserName(), originalFilename);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传数据文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传数据文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 上传考生降水落区文件
     */
    @ApiOperation(value = "上传考生降水落区文件")
    @PostMapping("/upload/precipitation-area")
    public ApiRest<Map<String, Object>> uploadPrecipitationAreaFile(@RequestParam("file") MultipartFile file,
                                                                    HttpServletRequest request) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return super.failure("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                return super.failure("文件名不能为空");
            }


            // 创建上传目录
            String uploadDir = weatherUploadConfig.getPrecipitationAreaDir();
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                log.info("创建降水落区文件上传目录: {} - {}", uploadDir, created ? "成功" : "失败");
            }

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFilename);
            String filePath = uploadDir + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            Files.copy(file.getInputStream(), targetPath);

            // 读取文件内容并解析
            String content = new String(Files.readAllBytes(targetPath), StandardCharsets.UTF_8);
            Map<String, Object> parsedData = parsePrecipitationAreaFile(content);



            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", System.currentTimeMillis());
            result.put("name", originalFilename);
            result.put("fileName", fileName);
            result.put("size", file.getSize());
            // 动态生成访问URL
            result.put("url", generateAccessUrl(request, "precipitation-area/" + fileName));
            // 返回绝对路径，便于后续读取
            result.put("filePath", filePath);
            // 同时返回相对路径，保持兼容性
            result.put("relativePath", "weather/precipitation-area/" + fileName);
            result.put("parsedData", parsedData);

            log.info("用户 {} 上传了降水落区实况文件：{} -> {}",
                UserUtils.getUser().getUserName(), originalFilename, filePath);

            return super.success(result);

        } catch (IOException e) {
            log.error("上传降水落区实况文件失败", e);
            return super.failure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传降水落区实况文件异常", e);
            return super.failure("文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 解析降水落区文件内容
     */
    private Map<String, Object> parsePrecipitationAreaFile(String content) {
        Map<String, Object> result = new HashMap<>();

        try {
            String[] lines = content.split("\n");
            Map<String, List<Map<String, Object>>> precipitationData = new HashMap<>();

            boolean inContoursSection = false;
            String currentLevel = null;
            List<List<Double>> currentCoordinates = new ArrayList<>();

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i].trim();

                // 检测闭合轮廓段开始
                if (line.startsWith("CLOSED_CONTOURS:")) {
                    inContoursSection = true;
                    continue;
                }


                // 解析数据行 (如: "3 55" 表示3无用，55个坐标点; "100 1" 表示降水量100，1个无用坐标点)
                Pattern levelPattern = Pattern.compile("^([\\d.]+)\\s+(\\d+)$");
                Matcher levelMatcher = levelPattern.matcher(line);
                if (levelMatcher.matches()) {
                    double firstNum = Double.parseDouble(levelMatcher.group(1));
                    int pointCount = Integer.parseInt(levelMatcher.group(2));

                    // 如果点数为1，这是降水量结束标识
                    if (pointCount == 1) {
                        // 保存当前轮廓，使用第一个数字作为降水量
                        if (currentLevel != null && !currentCoordinates.isEmpty()) {
                            // 使用结束标识中的降水量值更新当前轮廓的量级
                            String precipitationLevel = mapLevelToString(firstNum);
                            // 创建坐标副本，避免引用被清空
                            List<List<Double>> coordinatesCopy = new ArrayList<>(currentCoordinates);
                            addContourToData(precipitationData, precipitationLevel, coordinatesCopy);
                            currentCoordinates.clear();
                            currentLevel = null;
                        }
                        // 跳过下一行的无用坐标数据
                        if (i + 1 < lines.length) {
                            i++; // 跳过下一行
                        }
                        continue;
                    }

                    // 保存上一个轮廓（如果有的话）
                    if (currentLevel != null && !currentCoordinates.isEmpty()) {
                        // 创建坐标副本，避免引用被清空
                        List<List<Double>> coordinatesCopy = new ArrayList<>(currentCoordinates);
                        addContourToData(precipitationData, currentLevel, coordinatesCopy);
                    }

                    // 开始新轮廓，第一个数字无用，只记录要读取的坐标点数
                    currentLevel = "parsing"; // 临时标识，真正的量级在结束标识中
                    currentCoordinates = new ArrayList<>();
                    continue;
                }

                // 解析坐标行（包含多个坐标点）
                Pattern coordPattern = Pattern.compile("^\\s*[\\d.-]+\\s+[\\d.-]+\\s+[\\d.-]+");
                Matcher coordMatcher = coordPattern.matcher(line);
                if (coordMatcher.find() && currentLevel != null && !"null".equals(currentLevel)) {
                    String[] coords = line.trim().split("\\s+");

                    for (int j = 0; j < coords.length; j += 3) {
                        if (j + 1 < coords.length) {
                            try {
                                double lng = Double.parseDouble(coords[j]);
                                double lat = Double.parseDouble(coords[j + 1]);
                                List<Double> point = Arrays.asList(lng, lat);
                                currentCoordinates.add(point);
                            } catch (NumberFormatException e) {
                                log.warn("解析坐标失败: {}", Arrays.toString(coords));
                            }
                        }
                    }
                }
            }

            // 保存最后一个轮廓（如果文件结束时还有未保存的轮廓）
            if (currentLevel != null && !currentCoordinates.isEmpty()) {
                // 如果没有结束标识，使用默认量级
                String defaultLevel = "level0";
                log.warn("发现没有结束标识的轮廓数据，使用默认量级: {}", defaultLevel);
                // 创建坐标副本，避免引用被清空
                List<List<Double>> coordinatesCopy = new ArrayList<>(currentCoordinates);
                addContourToData(precipitationData, defaultLevel, coordinatesCopy);
            }

            result.put("success", true);
            result.put("data", precipitationData);
            result.put("message", "文件解析成功");

            log.info("降水落区文件解析完成，共解析出 {} 个量级的数据", precipitationData.size());

        } catch (Exception e) {
            log.error("解析降水落区文件失败", e);
            result.put("success", false);
            result.put("message", "文件解析失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 添加轮廓到数据中
     */
    private void addContourToData(Map<String, List<Map<String, Object>>> precipitationData,
                                  String level, List<List<Double>> coordinates) {
        log.debug("addContourToData 被调用: level={}, coordinates.size()={}", level, coordinates.size());
        if (coordinates.isEmpty()) {
            log.warn("坐标列表为空，跳过添加轮廓");
            return;
        }

        // 确保轮廓闭合
        if (!coordinates.get(0).equals(coordinates.get(coordinates.size() - 1))) {
            coordinates.add(new ArrayList<>(coordinates.get(0)));
        }

        // 使用JTS综合处理：删除小相交区域 + 平滑化处理
        List<List<Double>> processedCoordinates = coordinates;
        try {
            // 只有当坐标点数量足够多时才进行处理
            if (coordinates.size() > 10) {
                // 首先尝试常规综合处理
                processedCoordinates = GeometryUtils.processPolygonComprehensively(coordinates);

                // 如果常规处理后仍有问题，使用超强力处理
                if (!GeometryUtils.isValidPolygon(processedCoordinates)) {
                    log.debug("常规处理无效，使用超强力处理方法");
                    processedCoordinates = GeometryUtils.processPolygonUltraComprehensively(coordinates);
                }

                log.debug("几何图形综合处理完成: 原始点数={}, 处理后点数={}, 最终有效={}",
                    coordinates.size(), processedCoordinates.size(),
                    GeometryUtils.isValidPolygon(processedCoordinates));
            }
        } catch (Exception e) {
            log.warn("几何图形处理失败，使用原始坐标: {}", e.getMessage());
            processedCoordinates = coordinates;
        }

        Map<String, Object> contour = new HashMap<>();
        contour.put("id", System.currentTimeMillis() + "_" + Math.random());

        Map<String, Object> geometry = new HashMap<>();
        geometry.put("type", "Polygon");
        // GeoJSON Polygon 格式: [[[lng, lat], [lng, lat], ...]]
        geometry.put("coordinates", Arrays.asList(processedCoordinates));
        contour.put("geometry", geometry);

        Map<String, Object> properties = new HashMap<>();
        properties.put("precipitationLevel", level);
        properties.put("createTime", new Date().toString());
        contour.put("properties", properties);

        precipitationData.computeIfAbsent(level, k -> new ArrayList<>()).add(contour);
    }

    /**
     * 智能识别降水等级：根据数值范围自动分类
     */
    private String mapLevelToString(double level) {
        // 根据降水量范围智能识别等级
        if (level >= 0.1 && level < 10.0) {
            return "level0"; // 小雨：0.1-9.9mm
        } else if (level >= 10.0 && level < 25.0) {
            return "level10"; // 中雨：10-24.9mm
        } else if (level >= 25.0 && level < 50.0) {
            return "level25"; // 大雨：25-49.9mm
        } else if (level >= 50.0 && level < 100.0) {
            return "level50"; // 暴雨：50-99.9mm
        } else if (level >= 100.0 && level < 250.0) {
            return "level100"; // 大暴雨：100-249.9mm
        } else if (level >= 250.0) {
            return "level250"; // 特大暴雨：≥250mm
        } else {
            // 小于0.1的值或无效值，默认返回原始level标识
            return "level" + level;
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf('.') == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
    }

    /**
     * 判断是否为MICAPS文件
     */
    private boolean isMicapsFile(String extension) {
        String[] micapsExtensions = {"000", "024", "dat", "txt", "nc", "grib", "grib2", "cma"};
        for (String ext : micapsExtensions) {
            if (ext.equals(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为压缩文件
     */
    private boolean isArchiveFile(String extension) {
        String[] archiveExtensions = {"zip", "rar", "7z", "tar", "gz", "bz2"};
        for (String ext : archiveExtensions) {
            if (ext.equals(extension)) {
                return true;
            }
        }
        return false;
    }



    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid + (extension.isEmpty() ? "" : "." + extension);
    }
}
