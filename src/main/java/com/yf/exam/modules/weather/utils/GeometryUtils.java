package com.yf.exam.modules.weather.utils;

import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.geom.impl.CoordinateArraySequence;
import org.locationtech.jts.operation.buffer.BufferOp;
import org.locationtech.jts.operation.buffer.BufferParameters;
import org.locationtech.jts.simplify.DouglasPeuckerSimplifier;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 几何图形处理工具类
 * 使用JTS库处理几何图形的平滑化、去除尖刺等操作
 */
@Slf4j
public class GeometryUtils {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory();
    
    /**
     * 默认的简化容差（度）
     * 这个值决定了简化的程度，值越大简化越明显
     */
    private static final double DEFAULT_SIMPLIFY_TOLERANCE = 0.001;

    /**
     * 默认的缓冲区距离（度）
     * 用于平滑化处理的缓冲区大小
     */
    private static final double DEFAULT_BUFFER_DISTANCE = 0.0005;

    /**
     * 最小处理点数阈值
     * 少于此数量的坐标点不进行复杂处理
     */
    private static final int MIN_POINTS_FOR_PROCESSING = 8;

    /**
     * 大多边形点数阈值
     * 超过此数量的点使用更激进的简化策略
     */
    private static final int LARGE_POLYGON_THRESHOLD = 100;

    /**
     * 平滑化多边形坐标，去除不规则的"尖刺"（优化版本）
     *
     * @param coordinates 原始坐标列表 [[lng, lat], [lng, lat], ...]
     * @return 平滑化后的坐标列表
     */
    public static List<List<Double>> smoothPolygonCoordinates(List<List<Double>> coordinates) {
        // 快速检查：点数太少不需要处理
        if (coordinates == null || coordinates.size() < MIN_POINTS_FOR_PROCESSING) {
            return coordinates;
        }

        // 根据多边形大小选择不同的处理策略
        if (coordinates.size() > LARGE_POLYGON_THRESHOLD) {
            // 大多边形使用更激进的简化
            return smoothPolygonCoordinates(coordinates, DEFAULT_SIMPLIFY_TOLERANCE * 2, DEFAULT_BUFFER_DISTANCE);
        } else {
            return smoothPolygonCoordinates(coordinates, DEFAULT_SIMPLIFY_TOLERANCE, DEFAULT_BUFFER_DISTANCE);
        }
    }

    /**
     * 平滑化多边形坐标，去除不规则的"尖刺"
     * 
     * @param coordinates 原始坐标列表 [[lng, lat], [lng, lat], ...]
     * @param simplifyTolerance 简化容差
     * @param bufferDistance 缓冲区距离
     * @return 平滑化后的坐标列表
     */
    public static List<List<Double>> smoothPolygonCoordinates(List<List<Double>> coordinates, 
                                                             double simplifyTolerance, 
                                                             double bufferDistance) {
        try {
            if (coordinates == null || coordinates.size() < 3) {
                log.warn("坐标数据无效，至少需要3个点");
                return coordinates;
            }

            // 转换为JTS Polygon
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            if (polygon == null || !polygon.isValid()) {
                log.warn("无法创建有效的多边形");
                return coordinates;
            }

            // 第一步：使用Douglas-Peucker算法简化多边形，去除冗余点
            Geometry simplified = DouglasPeuckerSimplifier.simplify(polygon, simplifyTolerance);
            
            // 第二步：使用缓冲区操作平滑化边界
            // 先向外缓冲，再向内缓冲，可以平滑尖锐的角度
            Geometry buffered = BufferOp.bufferOp(simplified, bufferDistance, 
                BufferParameters.DEFAULT_QUADRANT_SEGMENTS, BufferParameters.CAP_ROUND);
            Geometry smoothed = BufferOp.bufferOp(buffered, -bufferDistance, 
                BufferParameters.DEFAULT_QUADRANT_SEGMENTS, BufferParameters.CAP_ROUND);

            // 如果平滑化后的几何图形无效，返回简化后的结果
            if (smoothed == null || smoothed.isEmpty() || !smoothed.isValid()) {
                log.warn("平滑化处理失败，使用简化后的结果");
                smoothed = simplified;
            }

            // 转换回坐标列表
            return extractCoordinatesFromGeometry(smoothed);

        } catch (Exception e) {
            log.error("平滑化多边形坐标失败", e);
            return coordinates; // 出错时返回原始坐标
        }
    }

    /**
     * 从坐标列表创建JTS Polygon
     */
    private static Polygon createPolygonFromCoordinates(List<List<Double>> coordinates) {
        try {
            // 确保多边形闭合
            List<List<Double>> closedCoords = new ArrayList<>(coordinates);
            if (!closedCoords.get(0).equals(closedCoords.get(closedCoords.size() - 1))) {
                closedCoords.add(new ArrayList<>(closedCoords.get(0)));
            }

            // 转换为JTS Coordinate数组
            Coordinate[] coords = new Coordinate[closedCoords.size()];
            for (int i = 0; i < closedCoords.size(); i++) {
                List<Double> point = closedCoords.get(i);
                coords[i] = new Coordinate(point.get(0), point.get(1));
            }

            // 创建线性环
            LinearRing shell = GEOMETRY_FACTORY.createLinearRing(coords);
            
            // 创建多边形
            return GEOMETRY_FACTORY.createPolygon(shell);
            
        } catch (Exception e) {
            log.error("创建多边形失败", e);
            return null;
        }
    }

    /**
     * 从JTS几何图形提取坐标
     */
    private static List<List<Double>> extractCoordinatesFromGeometry(Geometry geometry) {
        List<List<Double>> result = new ArrayList<>();
        
        try {
            if (geometry instanceof Polygon) {
                Polygon polygon = (Polygon) geometry;
                Coordinate[] coords = polygon.getExteriorRing().getCoordinates();
                
                for (Coordinate coord : coords) {
                    result.add(Arrays.asList(coord.x, coord.y));
                }
                
            } else if (geometry instanceof MultiPolygon) {
                // 如果是多多边形，取面积最大的一个
                MultiPolygon multiPolygon = (MultiPolygon) geometry;
                Polygon largestPolygon = null;
                double maxArea = 0;
                
                for (int i = 0; i < multiPolygon.getNumGeometries(); i++) {
                    Polygon poly = (Polygon) multiPolygon.getGeometryN(i);
                    double area = poly.getArea();
                    if (area > maxArea) {
                        maxArea = area;
                        largestPolygon = poly;
                    }
                }
                
                if (largestPolygon != null) {
                    Coordinate[] coords = largestPolygon.getExteriorRing().getCoordinates();
                    for (Coordinate coord : coords) {
                        result.add(Arrays.asList(coord.x, coord.y));
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("提取几何图形坐标失败", e);
        }
        
        return result;
    }

    /**
     * 验证多边形是否有效
     */
    public static boolean isValidPolygon(List<List<Double>> coordinates) {
        try {
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            return polygon != null && polygon.isValid();
        } catch (Exception e) {
            log.warn("验证多边形有效性失败", e);
            return false;
        }
    }

    /**
     * 计算多边形面积（平方度）
     */
    public static double calculatePolygonArea(List<List<Double>> coordinates) {
        try {
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            return polygon != null ? polygon.getArea() : 0.0;
        } catch (Exception e) {
            log.warn("计算多边形面积失败", e);
            return 0.0;
        }
    }

    /**
     * 删除线段相交产生的小区域
     * 当多边形边界线相交时，会产生小的三角形或其他小区域，此方法会识别并删除这些区域
     *
     * @param coordinates 原始坐标列表
     * @param minAreaThreshold 最小面积阈值（平方度），小于此值的区域将被删除
     * @return 处理后的坐标列表
     */
    public static List<List<Double>> removeSmallIntersectionAreas(List<List<Double>> coordinates,
                                                                 double minAreaThreshold) {
        try {
            if (coordinates == null || coordinates.size() < 3) {
                return coordinates;
            }

            // 创建多边形
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            if (polygon == null) {
                return coordinates;
            }

            // 如果多边形无效（通常是因为自相交），使用更强力的修复方法
            if (!polygon.isValid()) {
                log.debug("检测到无效多边形（可能自相交），使用强力修复方法");
                return fixSelfIntersectingPolygon(coordinates, minAreaThreshold);
            }

            // 对于有效多边形，使用缓冲区操作来删除小的突出部分
            return applyBufferCleaning(polygon, minAreaThreshold, coordinates);

        } catch (Exception e) {
            log.error("删除小相交区域失败", e);
            return coordinates;
        }
    }

    /**
     * 修复自相交多边形
     */
    private static List<List<Double>> fixSelfIntersectingPolygon(List<List<Double>> coordinates,
                                                               double minAreaThreshold) {
        try {
            // 方法1: 使用更大的缓冲区来强制修复自相交
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            if (polygon == null) {
                return coordinates;
            }

            // 使用较大的向内缓冲区来消除自相交
            double[] bufferSizes = {-0.0005, -0.001, -0.002}; // 逐步增大缓冲区

            for (double bufferSize : bufferSizes) {
                try {
                    Geometry buffered = polygon.buffer(bufferSize);
                    if (buffered != null && !buffered.isEmpty()) {
                        // 恢复原始大小
                        Geometry restored = buffered.buffer(-bufferSize);
                        if (restored != null && !restored.isEmpty() && restored.isValid()) {
                            return processMultiPolygonResult(restored, minAreaThreshold, coordinates);
                        }
                    }
                } catch (Exception e) {
                    log.debug("缓冲区大小 {} 处理失败，尝试下一个", bufferSize);
                }
            }

            // 方法2: 如果缓冲区方法失败，尝试使用凸包
            log.debug("缓冲区方法失败，尝试使用凸包方法");
            Geometry convexHull = polygon.convexHull();
            if (convexHull != null && convexHull.isValid() && convexHull instanceof Polygon) {
                // 凸包可能过于简化，所以只在原多边形严重自相交时使用
                double originalArea = polygon.getArea();
                double convexArea = convexHull.getArea();
                if (convexArea > 0 && (convexArea / originalArea) < 2.0) { // 凸包面积不超过原面积的2倍
                    return extractCoordinatesFromGeometry(convexHull);
                }
            }

            // 方法3: 最后的备选方案 - 使用简化算法
            log.debug("尝试使用简化算法处理自相交");
            Geometry simplified = DouglasPeuckerSimplifier.simplify(polygon, 0.005); // 更大的简化容差
            if (simplified != null && simplified.isValid()) {
                return extractCoordinatesFromGeometry(simplified);
            }

            return coordinates; // 所有方法都失败，返回原始坐标

        } catch (Exception e) {
            log.error("修复自相交多边形失败", e);
            return coordinates;
        }
    }

    /**
     * 应用缓冲区清理
     */
    private static List<List<Double>> applyBufferCleaning(Polygon polygon, double minAreaThreshold,
                                                         List<List<Double>> originalCoordinates) {
        try {
            // 使用多级缓冲区处理
            double[] bufferSizes = {-0.0001, -0.0002, -0.0005}; // 从小到大尝试

            for (double bufferSize : bufferSizes) {
                try {
                    Geometry buffered = polygon.buffer(bufferSize);
                    if (buffered != null && !buffered.isEmpty()) {
                        Geometry restored = buffered.buffer(-bufferSize);
                        if (restored != null && !restored.isEmpty() && restored.isValid()) {
                            List<List<Double>> result = processMultiPolygonResult(restored, minAreaThreshold, originalCoordinates);
                            if (result != originalCoordinates) { // 如果处理成功
                                return result;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.debug("缓冲区大小 {} 处理失败", bufferSize);
                }
            }

            return originalCoordinates;

        } catch (Exception e) {
            log.error("应用缓冲区清理失败", e);
            return originalCoordinates;
        }
    }

    /**
     * 处理多多边形结果
     */
    private static List<List<Double>> processMultiPolygonResult(Geometry geometry, double minAreaThreshold,
                                                               List<List<Double>> fallbackCoordinates) {
        try {
            if (geometry instanceof MultiPolygon) {
                MultiPolygon multiPolygon = (MultiPolygon) geometry;
                List<Polygon> validPolygons = new ArrayList<>();

                for (int i = 0; i < multiPolygon.getNumGeometries(); i++) {
                    Polygon poly = (Polygon) multiPolygon.getGeometryN(i);
                    if (poly.getArea() >= minAreaThreshold) {
                        validPolygons.add(poly);
                    } else {
                        log.debug("删除小区域，面积: {} 平方度", poly.getArea());
                    }
                }

                if (validPolygons.isEmpty()) {
                    return fallbackCoordinates;
                }

                // 选择面积最大的多边形
                Polygon largestPolygon = validPolygons.stream()
                    .max(Comparator.comparing(p -> p.getArea()))
                    .orElse(null);

                if (largestPolygon != null) {
                    return extractCoordinatesFromGeometry(largestPolygon);
                }
            } else if (geometry instanceof Polygon) {
                Polygon polygon = (Polygon) geometry;
                if (polygon.getArea() >= minAreaThreshold) {
                    return extractCoordinatesFromGeometry(polygon);
                }
            }

            return extractCoordinatesFromGeometry(geometry);

        } catch (Exception e) {
            log.error("处理多多边形结果失败", e);
            return fallbackCoordinates;
        }
    }

    /**
     * 删除线段相交产生的小区域（使用默认阈值，优化版本）
     * 默认最小面积阈值为 0.0001 平方度
     */
    public static List<List<Double>> removeSmallIntersectionAreas(List<List<Double>> coordinates) {
        // 快速检查：点数太少不需要处理
        if (coordinates == null || coordinates.size() < MIN_POINTS_FOR_PROCESSING) {
            return coordinates;
        }

        // 根据多边形大小动态调整阈值
        double threshold = coordinates.size() > LARGE_POLYGON_THRESHOLD ? 0.0002 : 0.0001;
        return removeSmallIntersectionAreas(coordinates, threshold);
    }

    /**
     * 强力清理自相交多边形（优化版本）
     * 使用更激进的方法来处理严重的自相交问题
     *
     * @param coordinates 原始坐标列表
     * @return 处理后的坐标列表
     */
    public static List<List<Double>> aggressiveCleanIntersections(List<List<Double>> coordinates) {
        try {
            if (coordinates == null || coordinates.size() < MIN_POINTS_FOR_PROCESSING) {
                return coordinates;
            }

            // 预处理：如果点数过多，先进行粗略简化
            List<List<Double>> workingCoordinates = coordinates;
            if (coordinates.size() > LARGE_POLYGON_THRESHOLD * 2) {
                workingCoordinates = preSimplifyLargePolygon(coordinates);
                log.debug("大多边形预简化: {} -> {} 个点", coordinates.size(), workingCoordinates.size());
            }

            // 创建多边形
            Polygon polygon = createPolygonFromCoordinates(workingCoordinates);
            if (polygon == null) {
                return coordinates;
            }

            // 优化的缓冲区策略：根据多边形大小选择缓冲区大小
            double[] bufferSizes = selectOptimalBufferSizes(workingCoordinates.size());

            for (double bufferSize : bufferSizes) {
                try {
                    Geometry result = applyOptimizedBuffering(polygon, bufferSize);
                    if (result != null) {
                        log.debug("强力清理成功，使用缓冲区大小: {}", bufferSize);
                        return extractCoordinatesFromGeometry(result);
                    }
                } catch (Exception e) {
                    log.debug("缓冲区 {} 失败，尝试下一个", bufferSize);
                }
            }

            // 备选方案：快速凸包处理
            return applyFallbackMethods(polygon, coordinates);

        } catch (Exception e) {
            log.error("强力清理自相交失败", e);
            return coordinates;
        }
    }

    /**
     * 预简化大多边形
     */
    private static List<List<Double>> preSimplifyLargePolygon(List<List<Double>> coordinates) {
        try {
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            if (polygon != null) {
                // 使用较大的简化容差进行预处理
                Geometry simplified = DouglasPeuckerSimplifier.simplify(polygon, 0.01);
                if (simplified != null && simplified.isValid()) {
                    return extractCoordinatesFromGeometry(simplified);
                }
            }
        } catch (Exception e) {
            log.debug("预简化失败");
        }
        return coordinates;
    }

    /**
     * 选择最优的缓冲区大小
     */
    private static double[] selectOptimalBufferSizes(int pointCount) {
        if (pointCount > LARGE_POLYGON_THRESHOLD) {
            // 大多边形使用更大的缓冲区
            return new double[]{-0.002, -0.005, -0.01};
        } else if (pointCount > 50) {
            // 中等多边形
            return new double[]{-0.001, -0.002, -0.005};
        } else {
            // 小多边形使用精细缓冲区
            return new double[]{-0.0005, -0.001, -0.002};
        }
    }

    /**
     * 应用优化的缓冲区处理
     */
    private static Geometry applyOptimizedBuffering(Polygon polygon, double bufferSize) {
        try {
            Geometry buffered = polygon.buffer(bufferSize);
            if (buffered != null && !buffered.isEmpty()) {
                Geometry restored = buffered.buffer(-bufferSize);
                if (restored != null && !restored.isEmpty() && restored.isValid()) {
                    // 快速选择最大多边形
                    return selectLargestPolygon(restored);
                }
            }
        } catch (Exception e) {
            log.debug("缓冲区处理失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 快速选择最大多边形
     */
    private static Geometry selectLargestPolygon(Geometry geometry) {
        if (geometry instanceof MultiPolygon) {
            MultiPolygon mp = (MultiPolygon) geometry;
            Polygon largest = null;
            double maxArea = 0;

            for (int i = 0; i < mp.getNumGeometries(); i++) {
                Polygon p = (Polygon) mp.getGeometryN(i);
                double area = p.getArea();
                if (area > maxArea) {
                    maxArea = area;
                    largest = p;
                }
            }
            return largest;
        }
        return geometry;
    }

    /**
     * 应用备选方法
     */
    private static List<List<Double>> applyFallbackMethods(Polygon polygon, List<List<Double>> originalCoordinates) {
        // 方法1: 凸包
        try {
            Geometry convexHull = polygon.convexHull();
            if (convexHull != null && convexHull.isValid()) {
                // 检查凸包是否过于简化
                double originalArea = polygon.getArea();
                double convexArea = convexHull.getArea();
                if (originalArea > 0 && (convexArea / originalArea) < 3.0) { // 凸包面积不超过原面积的3倍
                    log.debug("使用凸包方法");
                    return extractCoordinatesFromGeometry(convexHull);
                }
            }
        } catch (Exception e) {
            log.debug("凸包方法失败");
        }

        // 方法2: 智能重建
        log.debug("使用智能重建方法");
        return rebuildSimplifiedPolygon(originalCoordinates);
    }

    /**
     * 重建简化多边形（优化版本）
     * 通过智能采样关键点来重建一个简化的多边形
     */
    private static List<List<Double>> rebuildSimplifiedPolygon(List<List<Double>> coordinates) {
        try {
            if (coordinates.size() <= MIN_POINTS_FOR_PROCESSING) {
                return coordinates; // 点太少，不需要重建
            }

            // 智能采样策略
            List<List<Double>> simplified = smartSamplePoints(coordinates);

            // 验证重建的多边形
            if (simplified.size() >= 4) { // 至少需要4个点（包括闭合点）
                Polygon rebuilt = createPolygonFromCoordinates(simplified);
                if (rebuilt != null && rebuilt.isValid()) {
                    log.debug("智能重建多边形成功，从 {} 个点简化为 {} 个点", coordinates.size(), simplified.size());
                    return simplified;
                }
            }

            // 如果智能采样失败，使用简单均匀采样
            return simpleUniformSample(coordinates);

        } catch (Exception e) {
            log.error("重建简化多边形失败", e);
            return coordinates;
        }
    }

    /**
     * 智能采样点
     * 保留重要的转折点和边界点
     */
    private static List<List<Double>> smartSamplePoints(List<List<Double>> coordinates) {
        List<List<Double>> sampled = new ArrayList<>();

        // 计算目标点数
        int targetPoints = Math.min(20, Math.max(8, coordinates.size() / 10));

        if (coordinates.size() <= targetPoints) {
            return new ArrayList<>(coordinates);
        }

        // 始终保留第一个点
        sampled.add(new ArrayList<>(coordinates.get(0)));

        // 计算每个点的重要性（基于角度变化）
        List<Double> importance = calculatePointImportance(coordinates);

        // 选择最重要的点
        List<Integer> selectedIndices = selectImportantPoints(importance, targetPoints - 2); // -2 因为已经有首尾点

        // 按顺序添加选中的点
        selectedIndices.sort(Integer::compareTo);
        for (int index : selectedIndices) {
            if (index > 0 && index < coordinates.size() - 1) { // 避免重复添加首尾点
                sampled.add(new ArrayList<>(coordinates.get(index)));
            }
        }

        // 确保闭合
        if (!sampled.get(0).equals(coordinates.get(coordinates.size() - 1))) {
            sampled.add(new ArrayList<>(coordinates.get(0)));
        }

        return sampled;
    }

    /**
     * 计算每个点的重要性
     */
    private static List<Double> calculatePointImportance(List<List<Double>> coordinates) {
        List<Double> importance = new ArrayList<>();

        for (int i = 0; i < coordinates.size(); i++) {
            if (i == 0 || i == coordinates.size() - 1) {
                importance.add(1.0); // 首尾点最重要
            } else {
                // 计算角度变化来确定重要性
                double angle = calculateAngleChange(coordinates, i);
                importance.add(Math.abs(angle));
            }
        }

        return importance;
    }

    /**
     * 计算角度变化
     */
    private static double calculateAngleChange(List<List<Double>> coordinates, int index) {
        if (index <= 0 || index >= coordinates.size() - 1) {
            return 0.0;
        }

        List<Double> prev = coordinates.get(index - 1);
        List<Double> curr = coordinates.get(index);
        List<Double> next = coordinates.get(index + 1);

        // 计算两个向量的角度
        double dx1 = curr.get(0) - prev.get(0);
        double dy1 = curr.get(1) - prev.get(1);
        double dx2 = next.get(0) - curr.get(0);
        double dy2 = next.get(1) - curr.get(1);

        double angle1 = Math.atan2(dy1, dx1);
        double angle2 = Math.atan2(dy2, dx2);

        double angleDiff = angle2 - angle1;

        // 标准化角度到 [-π, π]
        while (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
        while (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;

        return angleDiff;
    }

    /**
     * 选择重要的点
     */
    private static List<Integer> selectImportantPoints(List<Double> importance, int count) {
        List<Integer> indices = new ArrayList<>();

        // 创建索引-重要性对
        List<int[]> pairs = new ArrayList<>();
        for (int i = 1; i < importance.size() - 1; i++) { // 跳过首尾点
            pairs.add(new int[]{i, importance.get(i).intValue() * 1000}); // 放大以便排序
        }

        // 按重要性排序
        pairs.sort((a, b) -> Integer.compare(b[1], a[1]));

        // 选择前N个最重要的点
        for (int i = 0; i < Math.min(count, pairs.size()); i++) {
            indices.add(pairs.get(i)[0]);
        }

        return indices;
    }

    /**
     * 简单均匀采样（备选方案）
     */
    private static List<List<Double>> simpleUniformSample(List<List<Double>> coordinates) {
        List<List<Double>> sampled = new ArrayList<>();
        int step = Math.max(1, coordinates.size() / 15); // 保留约15个点

        for (int i = 0; i < coordinates.size() - 1; i += step) {
            sampled.add(new ArrayList<>(coordinates.get(i)));
        }

        // 确保闭合
        if (!sampled.isEmpty() && !sampled.get(0).equals(sampled.get(sampled.size() - 1))) {
            sampled.add(new ArrayList<>(sampled.get(0)));
        }

        return sampled;
    }

    /**
     * 综合处理多边形：删除小相交区域 + 平滑化
     *
     * @param coordinates 原始坐标列表
     * @return 处理后的坐标列表
     */
    public static List<List<Double>> processPolygonComprehensively(List<List<Double>> coordinates) {
        try {
            if (coordinates == null || coordinates.size() < 3) {
                return coordinates;
            }

            // 第一步：尝试常规方法删除小的相交区域
            List<List<Double>> cleanedCoordinates = removeSmallIntersectionAreas(coordinates);

            // 检查是否还有自相交问题
            boolean stillHasIssues = false;
            try {
                Polygon testPolygon = createPolygonFromCoordinates(cleanedCoordinates);
                if (testPolygon == null || !testPolygon.isValid()) {
                    stillHasIssues = true;
                }
            } catch (Exception e) {
                stillHasIssues = true;
            }

            // 如果常规方法无效，使用强力清理
            if (stillHasIssues) {
                log.debug("常规清理方法无效，使用强力清理方法");
                cleanedCoordinates = aggressiveCleanIntersections(coordinates);
            }

            // 第二步：平滑化处理
            List<List<Double>> smoothedCoordinates = smoothPolygonCoordinates(cleanedCoordinates);

            // 验证最终结果
            boolean finalValid = isValidPolygon(smoothedCoordinates);
            if (!finalValid) {
                log.warn("最终结果仍然无效，返回清理后的坐标");
                return cleanedCoordinates;
            }

            log.debug("综合处理完成: 原始点数={}, 清理后点数={}, 平滑化后点数={}, 最终有效={}",
                coordinates.size(), cleanedCoordinates.size(), smoothedCoordinates.size(), finalValid);

            return smoothedCoordinates;

        } catch (Exception e) {
            log.error("综合处理多边形失败", e);
            return coordinates;
        }
    }

    /**
     * 超强力综合处理（用于处理最困难的情况）
     *
     * @param coordinates 原始坐标列表
     * @return 处理后的坐标列表
     */
    public static List<List<Double>> processPolygonUltraComprehensively(List<List<Double>> coordinates) {
        try {
            if (coordinates == null || coordinates.size() < 3) {
                return coordinates;
            }

            // 直接使用强力清理
            List<List<Double>> cleanedCoordinates = aggressiveCleanIntersections(coordinates);

            // 然后进行平滑化
            List<List<Double>> smoothedCoordinates = smoothPolygonCoordinates(cleanedCoordinates);

            // 如果平滑化后仍有问题，返回清理后的结果
            if (!isValidPolygon(smoothedCoordinates)) {
                log.warn("平滑化后仍有问题，返回强力清理的结果");
                return cleanedCoordinates;
            }

            log.debug("超强力处理完成: 原始点数={}, 处理后点数={}",
                coordinates.size(), smoothedCoordinates.size());

            return smoothedCoordinates;

        } catch (Exception e) {
            log.error("超强力处理多边形失败", e);
            return coordinates;
        }
    }
}
