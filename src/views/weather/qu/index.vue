<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>历史个例试题管理</h2>
      <p>管理历史个例天气考试的试题，包括题目内容、Micaps数据文件和标准答案</p>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-container">
      <el-row :gutter="20">
        <el-col :span="18">
          <el-input
            v-model="listQuery.title"
            placeholder="请输入题目标题"
            style="width: 200px;"
            class="filter-item"
            clearable
          />
          <el-input
            v-model="listQuery.forecastDate"
            placeholder="起报日期"
            style="width: 150px;"
            class="filter-item"
            clearable
          />
          <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
          <el-button type="default" @click="resetQuery">重置</el-button>
        </el-col>
        <el-col :span="6" style="text-align: right;">
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增题目</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="questionList"
      border
      style="width: 100%"
    >
      <el-table-column label="题目标题" min-width="200">
        <template v-slot="scope">
          <div class="title-cell">
            {{ scope.row.title }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="forecastDate" label="起报日期" width="120" align="center" />

      <el-table-column prop="forecastTime" label="起报时次" width="100" align="center">
        <template v-slot="scope">
          {{ scope.row.forecastTime }}时
        </template>
      </el-table-column>

      <el-table-column label="站点数量" width="100" align="center">
        <template v-slot="scope">
          {{ getStationCount(scope.row) }}
        </template>
      </el-table-column>

      <el-table-column label="数据文件" width="160" align="center">
        <template v-slot="scope">
          <el-tag v-if="getDataFileName(scope.row)" type="success" size="mini">
            {{ getDataFileName(scope.row) }}
          </el-tag>
          <el-tag v-else type="info" size="mini">未上传</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="降水落区文件" width="120" align="center">
        <template v-slot="scope">
          <el-tag v-if="getPrecipitationAreaFileName(scope.row)" type="success" size="mini">
            已上传
          </el-tag>
          <el-tag v-else type="info" size="mini">未上传</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="标准答案" width="100" align="center">
        <template v-slot="scope">
          <el-tag v-if="hasStandardAnswer(scope.row)" type="success" size="mini">已设置</el-tag>
          <el-tag v-else type="warning" size="mini">未设置</el-tag>
        </template>
      </el-table-column>

      <!--      <el-table-column prop="totalScore" label="总分" width="80" align="center" />-->

      <el-table-column prop="createTime" label="创建时间" width="200" align="center" />

      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template v-slot="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" style="color: #f56c6c;" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 新增/编辑弹窗 - 整合版本 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="1600px"
      top="3vh"
      @close="resetForm"
    >
      <!-- 题目信息表单 -->
      <el-form
        ref="questionForm"
        :model="questionForm"
        :rules="formRules"
        label-width="120px"
        style="padding: 20px;"
      >
        <el-form-item label="题目标题" prop="title">
          <el-input v-model="questionForm.title" placeholder="请输入题目标题" />
        </el-form-item>

        <el-form-item label="起报时间配置">
          <div class="forecast-time-config">
            <div class="time-group">
              <span class="time-label">落区起报：</span>
              <el-date-picker
                v-model="questionForm.forecastStartDate"
                type="date"
                placeholder="起报日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 140px;"
              />
              <el-select
                v-model="questionForm.precipitationForecastTime"
                placeholder="时次"
                style="width: 80px; margin-left: 8px;"
              >
                <el-option label="08时" value="08" />
                <el-option label="20时" value="20" />
              </el-select>
            </div>
            <div class="time-group" style="margin-left: 30px;">
              <span class="time-label">站点起报：</span>
              <el-date-picker
                v-model="questionForm.forecastDate"
                type="date"
                placeholder="起报日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 140px;"
              />
              <el-select
                v-model="questionForm.forecastTime"
                placeholder="时次"
                style="width: 80px; margin-left: 8px;"
              >
                <el-option label="08时" value="08" />
                <el-option label="20时" value="20" />
              </el-select>
            </div>
          </div>
        </el-form-item>

        <!-- 第一部分：降水分级落区预报内容 -->
        <el-divider content-position="left">
          <h3 style="color: #409EFF; margin: 0;">第一部分：降水分级落区预报</h3>
        </el-divider>

        <el-form-item label="降水预报题目" prop="precipitationContent">
          <el-input
            v-model="questionForm.precipitationContent"
            :rows="3"
            type="textarea"
            placeholder="请输入降水分级落区预报的具体题目内容，例如：根据提供的气象资料，对指定区域进行24小时降水分级落区预报..."
          />
        </el-form-item>

        <el-form-item label="预报区域选择" prop="precipitationRegion">
          <el-select
            v-model="questionForm.precipitationRegion"
            placeholder="请选择预报区域"
            style="width: 200px;"
          >
            <el-option
              v-for="region in precipitationRegions"
              :key="region.value"
              :label="region.label"
              :value="region.value"
            />
          </el-select>
          <span style="margin-left: 15px; color: #909399; font-size: 14px;">
            学生将在WebGIS中对选定区域进行降水落区绘制
          </span>
        </el-form-item>

        <!-- 第二部分：灾害性天气预报 -->
        <el-divider content-position="left">
          <h3 style="color: #E6A23C; margin: 0;">第二部分：灾害性天气预报</h3>
        </el-divider>

        <el-form-item label="站点预报题目" prop="content">
          <el-input
            v-model="questionForm.content"
            :rows="4"
            type="textarea"
            placeholder="请输入题目内容描述"
          />
        </el-form-item>
        <el-form-item label="站点配置" prop="stations">
          <div class="station-config">
            <el-tag
              v-for="(station, index) in stationList"
              :key="index"
              closable
              style="margin-right: 10px; margin-bottom: 10px;"
              @close="removeStation(index)"
            >
              {{ station }}
            </el-tag>
            <el-input
              v-if="inputVisible"
              ref="saveTagInput"
              v-model="inputValue"
              size="small"
              style="width: 100px;"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            />
            <el-button v-else size="small" @click="showInput">+ 添加站点</el-button>
          </div>
        </el-form-item>

        <!-- 答案设置表格 -->
        <el-form-item v-if="stationList.length > 0" label="天气预报答案">
          <div class="answer-table-section">

            <!-- 标准答案表格 - 行显示站点，列显示要素 -->
            <div class="standard-answer-table">
              <el-table
                :data="stationList"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold', textAlign: 'center' }"
                :cell-style="{ textAlign: 'center' }"
                :row-key="(row, index) => row"
                border
                style="width: 100%;"
              >
                <!-- 站点名称列 -->
                <el-table-column label="站点名称" width="120" align="center" fixed="left">
                  <template v-slot="scope">
                    <el-tag type="info" size="mini">{{ scope.row }}</el-tag>
                  </template>
                </el-table-column>

                <!-- 风力等级列 -->
                <el-table-column :label="getTimeLabel() + '最大风力(级)'" min-width="140" align="center">
                  <template v-slot="scope">
                    <el-select
                      v-model="answerData[scope.row].windForce"
                      :class="{ 'required-field': !answerData[scope.row].windForce }"
                      size="mini"
                      placeholder="风力"
                      style="width: 100%;"
                    >
                      <el-option v-for="level in windLevels" :key="level" :label="level" :value="level" />
                    </el-select>
                  </template>
                </el-table-column>

                <!-- 风向列 -->
                <el-table-column :label="getTimeLabel() + '最大风力时的风向'" min-width="150" align="center">
                  <template v-slot="scope">
                    <el-select
                      v-model="answerData[scope.row].windDirection"
                      :class="{ 'required-field': !answerData[scope.row].windDirection }"
                      size="mini"
                      placeholder="风向"
                      style="width: 100%;"
                    >
                      <el-option v-for="direction in windDirections" :key="direction" :label="direction" :value="direction" />
                    </el-select>
                  </template>
                </el-table-column>

                <!-- 气温范围列 -->
                <el-table-column label="气温范围(℃)" min-width="200" align="center">
                  <template v-slot="scope">
                    <div class="temperature-range">
                      <el-input-number
                        v-model="answerData[scope.row].minTemperature"
                        :min="-50"
                        :max="50"
                        :precision="0"
                        :class="{ 'required-field': answerData[scope.row].minTemperature === null }"
                        size="mini"
                        placeholder="最低"
                        style="width: 80px;"
                        controls-position="right"
                      />
                      <span class="temp-separator">~</span>
                      <el-input-number
                        v-model="answerData[scope.row].maxTemperature"
                        :min="-50"
                        :max="50"
                        :precision="0"
                        :class="{ 'required-field': answerData[scope.row].maxTemperature === null }"
                        size="mini"
                        placeholder="最高"
                        style="width: 80px;"
                        controls-position="right"
                      />
                    </div>
                  </template>
                </el-table-column>

                <!-- 降水量级列 -->
                <el-table-column :label="getTimeLabel() + '降水(雨、雪)量级'" min-width="160" align="center">
                  <template v-slot="scope">
                    <el-select
                      v-model="answerData[scope.row].precipitation"
                      :class="{ 'required-field': !answerData[scope.row].precipitation }"
                      size="mini"
                      placeholder="降水"
                      style="width: 100%;"
                    >
                      <el-option label="无雨雪" value="无雨雪" />
                      <el-option label="雨夹雪" value="雨夹雪" />
                      <el-option label="小雨" value="小雨" />
                      <el-option label="小雪" value="小雪" />
                      <el-option label="中雨" value="中雨" />
                      <el-option label="中雪" value="中雪" />
                      <el-option label="大雨" value="大雨" />
                      <el-option label="暴雨" value="暴雨" />
                      <el-option label="大雪" value="大雪" />
                      <el-option label="大暴雨以上" value="大暴雨以上" />
                      <el-option label="暴雪" value="暴雪" />
                    </el-select>
                  </template>
                </el-table-column>

                <!-- 灾害性天气列 -->
                <el-table-column label="灾害性天气类型" min-width="150" align="center">
                  <template v-slot="scope">
                    <el-select
                      v-model="answerData[scope.row].disasterWeather"
                      :class="{ 'required-field': !answerData[scope.row].disasterWeather || answerData[scope.row].disasterWeather.length === 0 }"
                      size="mini"
                      placeholder="选择灾害天气"
                      style="width: 100%;"
                      multiple
                      collapse-tags
                    >
                      <el-option label="无" value="无" />
                      <el-option label="暴雨" value="暴雨" />
                      <el-option label="暴雪" value="暴雪" />
                      <el-option label="高温" value="高温" />
                      <el-option label="大风" value="大风" />
                      <el-option label="寒潮" value="寒潮" />
                      <el-option label="大雾" value="大雾" />
                      <el-option label="沙尘暴" value="沙尘暴" />
                    </el-select>
                  </template>
                </el-table-column>

                <!-- 完成状态列 -->
                <el-table-column label="完成状态" width="100" align="center" fixed="right">
                  <template v-slot="scope">
                    <el-tag
                      :type="isStationCompleteNew(scope.row) ? 'success' : 'warning'"
                      size="mini"
                    >
                      {{ isStationCompleteNew(scope.row) ? '已完成' : '未完成' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 完成进度统计 -->
            <div v-if="stationList.length > 0" class="progress-stats">
              <el-card shadow="never" style="margin-top: 15px;">

                <div class="progress-container">
                  <div class="progress-header">
                    <div class="progress-text-section">
                      <div slot="header" class="clearfix">
                        <span><i class="el-icon-data-line" /> 完成进度统计:</span>
                      </div>
                      <span class="progress-text">完成进度：{{ dynamicCompletionPercentage }}%</span>
                      <span class="progress-detail">({{ dynamicCompletedStations }}/{{ stationList.length }})</span>
                    </div>
                    <div class="status-indicators-inline">
                      <el-tag type="success" size="small" class="status-tag-inline">
                        <i class="el-icon-check" /> 已完成：{{ dynamicCompletedStations }}个
                      </el-tag>
                      <el-tag type="warning" size="small" class="status-tag-inline">
                        <i class="el-icon-time" /> 待完成：{{ stationList.length - dynamicCompletedStations }}个
                      </el-tag>
                    </div>
                  </div>
                  <el-progress
                    :percentage="dynamicCompletionPercentage"
                    :color="getProgressColor(dynamicCompletionPercentage)"
                    :stroke-width="12"
                    :show-text="false"
                    style="margin-top: 15px;"
                  />
                </div>
              </el-card>
            </div>
          </div>
        </el-form-item>

        <!-- 文件上传区域 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="个例数据文件">
              <div class="upload-container">
                <div class="file-upload-area">
                  <el-upload
                    ref="upload"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :data="uploadData"
                    :on-success="handleUploadSuccess"
                    :on-error="handleUploadError"
                    :before-upload="beforeUpload"
                    :file-list="dataFileList"
                    :on-remove="handleDataFileRemove"
                    accept=".zip,.rar,.7z,.tar,.gz,.bz2"
                    drag
                  >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将个例数据文件拖到此处，或<em>点击上传</em>
                    </div>
                    <div slot="tip" class="el-upload__tip">
                      支持格式：.zip, .rar, .7z, .tar, .gz, .bz2
                    </div>
                  </el-upload>
                </div>

                <div v-if="dataFileList.length > 0" class="file-list">
                  <h5>已上传文件：</h5>
                  <div class="file-items">
                    <div
                      v-for="file in dataFileList"
                      :key="file.uid || file.id"
                      class="file-item"
                    >
                      <div class="file-info">
                        <i class="el-icon-document" />
                        <span class="file-name">{{ file.name }}</span>
                        <span class="file-size">{{ formatFileSize(file.size) }}</span>
                      </div>
                      <div class="file-actions">
                        <el-button
                          type="text"
                          size="mini"
                          icon="el-icon-download"
                          @click="downloadDataFile(file)"
                        >
                          下载
                        </el-button>
                        <el-button
                          type="text"
                          size="mini"
                          style="color: #F56C6C;"
                          icon="el-icon-delete"
                          @click="removeDataFile(file)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="降水落区实况文件">
              <div class="upload-container">
                <div class="file-upload-area">
                  <el-upload
                    ref="precipitationAreaUpload"
                    :action="precipitationAreaUploadUrl"
                    :headers="uploadHeaders"
                    :data="precipitationAreaUploadData"
                    :on-success="handlePrecipitationAreaUploadSuccess"
                    :on-error="handlePrecipitationAreaUploadError"
                    :before-upload="beforePrecipitationAreaUpload"
                    :file-list="precipitationAreaFileList"
                    :on-remove="handlePrecipitationAreaFileRemove"
                    accept=".dat,.txt,.000,.024,.csv"
                    drag
                  >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将降水落区实况文件拖到此处，或<em>点击上传</em>
                    </div>
                    <div slot="tip" class="el-upload__tip">
                      支持格式：.dat, .txt, .000, .024, .csv
                    </div>
                  </el-upload>
                </div>

                <div v-if="precipitationAreaFileList.length > 0" class="file-list">
                  <h5>已上传文件：</h5>
                  <div class="file-items">
                    <div
                      v-for="file in precipitationAreaFileList"
                      :key="file.uid || file.id"
                      class="file-item"
                    >
                      <div class="file-info">
                        <i class="el-icon-document" />
                        <span class="file-name">{{ file.name }}</span>
                        <span class="file-size">{{ formatFileSize(file.size) }}</span>
                      </div>
                      <div class="file-actions">
                        <el-button
                          type="text"
                          size="mini"
                          icon="el-icon-download"
                          @click="downloadPrecipitationAreaFile(file)"
                        >
                          下载
                        </el-button>
                        <el-button
                          type="text"
                          size="mini"
                          style="color: #F56C6C;"
                          icon="el-icon-delete"
                          @click="removePrecipitationAreaFile(file)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="落区CMA文件">
              <div class="upload-container">
                <div class="file-upload-area">
                  <el-upload
                    ref="cmaUpload"
                    :action="cmaUploadUrl"
                    :headers="uploadHeaders"
                    :data="cmaUploadData"
                    :on-success="handleCmaUploadSuccess"
                    :on-error="handleCmaUploadError"
                    :before-upload="beforeCmaUpload"
                    :file-list="cmaFileList"
                    :on-remove="handleCmaFileRemove"
                    accept=".txt,.csv,.024"
                    drag
                  >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将落区CMA文件拖到此处，或<em>点击上传</em>
                    </div>
                    <div slot="tip" class="el-upload__tip">
                      支持格式：.txt, .csv, .024
                    </div>
                  </el-upload>
                </div>

                <div v-if="cmaFileList.length > 0" class="file-list">
                  <h5>已上传文件：</h5>
                  <div class="file-items">
                    <div
                      v-for="file in cmaFileList"
                      :key="file.uid || file.id"
                      class="file-item"
                    >
                      <div class="file-info">
                        <i class="el-icon-document" />
                        <span class="file-name">{{ file.name }}</span>
                        <span class="file-size">{{ formatFileSize(file.size) }}</span>
                      </div>
                      <div class="file-actions">
                        <el-button
                          type="text"
                          size="mini"
                          icon="el-icon-download"
                          @click="downloadCmaFile(file)"
                        >
                          下载
                        </el-button>
                        <el-button
                          type="text"
                          size="mini"
                          style="color: #F56C6C;"
                          icon="el-icon-delete"
                          @click="removeCmaFile(file)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 历史天气个例试题不需要设置总分，系统自动计算 -->
        <!-- <el-form-item label="总分" prop="totalScore">
              <el-input-number
                v-model="questionForm.totalScore"
                :min="1"
                :max="1000"
                :precision="2"
                style="width: 150px;"
              />
            </el-form-item> -->
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button v-if="stationList.length > 0" type="success" @click="previewAnswers">预览答案</el-button>
        <el-button :disabled="!canSubmit" type="primary" @click="submitIntegratedForm">
          <i class="el-icon-check" /> 保存
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { post } from '@/utils/request'
import { getToken } from '@/utils/auth'
import { getWeatherCaseList, saveWeatherCase, getWeatherCaseDetail, deleteWeatherCase } from '@/api/weather/weather'
import Pagination from '@/components/Pagination'

export default {
  name: 'WeatherQuestionManage',
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      questionList: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 10,
        title: '',
        forecastDate: ''
      },
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      questionForm: {
        id: '',
        title: '',
        content: '',
        precipitationContent: '', // 第一部分：降水预报题目内容
        precipitationRegion: '', // 第一部分：预报区域选择
        forecastDate: null,
        forecastTime: '08',
        forecastStartDate: null, // 落区预报起报时间
        precipitationForecastTime: '08', // 落区预报起报时次
        dataFilePath: '',
        dataFileName: '',
        observationFilePath: '', // 降水落区实况文件路径
        observationFileName: '', // 降水落区实况文件名
        cmaFilePath: '', // 落区CMA文件路径
        cmaFileName: '', // 落区CMA文件名
        totalScore: 100,
        analysis: '',
        remark: '',
        level: 1
      },
      stationList: [],
      inputVisible: false,
      inputValue: '',
      // 整合对话框相关
      // 答案设置相关
      answerTableData: [],
      // 动态答案数据 - 以站点为key的对象
      answerData: {},
      // 用于表格显示的虚拟行数据
      answerRowData: {},
      // 选项数据
      windLevels: ['静风', '一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级', '九级', '十级', '十一级', '十二级', '大于十二级'],
      windDirections: [
        '0.0～22.5°',
        '22.5～45.0°',
        '45.0～67.5°',
        '67.5～90.0°',
        '90.0～112.5°',
        '112.5～135.0°',
        '135.0～157.5°',
        '157.5～180.0°',
        '180.0～202.5°',
        '202.5～225.0°',
        '225.0～247.5°',
        '247.5～270.0°',
        '270.0～292.5°',
        '292.5～315.0°',
        '315.0～337.5°',
        '337.5～360.0°'
      ],
      // 降水预报区域选项
      precipitationRegions: [
        { value: 'region1', label: '区域一(华北区域)' },
        { value: 'region2', label: '区域二(东北区域)' },
        { value: 'region3', label: '区域三(长江中下游区域)' },
        { value: 'region4', label: '区域四(华南区域)' },
        { value: 'region5', label: '区域五(西南地区东部)' },
        { value: 'region6', label: '区域六(青藏高原区域)' },
        { value: 'region7', label: '区域七(新疆区域)' },
        { value: 'region8', label: '区域八(西北地区东部区域)' },
        { value: 'region9', label: '区域九(内蒙古区域)' }
      ],
      formRules: {
        title: [
          { required: true, message: '请输入题目标题', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入题目内容', trigger: 'blur' }
        ],
        forecastDate: [
          { required: true, message: '请选择站点个例起报时间', trigger: 'change' }
        ],
        forecastTime: [
          { required: true, message: '请选择站点个例起报时次', trigger: 'change' }
        ]
      },
      // 个例数据文件上传（使用专用接口）
      uploadUrl: process.env.VUE_APP_BASE_API + '/exam/api/weather/case/upload/data',
      uploadHeaders: {
        'token': getToken()
      },
      uploadData: {},
      // 降水落区实况文件上传相关（使用专用接口）
      precipitationAreaUploadUrl: process.env.VUE_APP_BASE_API + '/exam/api/weather/case/upload/precipitation-area',
      precipitationAreaUploadData: {},
      // CMA文件上传相关（使用专用接口）
      cmaUploadUrl: process.env.VUE_APP_BASE_API + '/exam/api/weather/case/upload/micaps',
      cmaUploadData: {},
      // 个例数据文件上传相关
      dataFileList: [],
      dataFileName: '',
      // 降水落区实况文件上传相关
      precipitationAreaFileList: [],
      // 落区CMA文件上传相关
      cmaFileList: []
    }
  },
  computed: {
    // 计算完成进度
    completionPercentage() {
      if (this.answerTableData.length === 0) return 0
      var completed = this.answerTableData.filter(station => this.isStationComplete(station)).length
      return Math.round((completed / this.answerTableData.length) * 100)
    },

    // 已完成站点数
    completedStations() {
      return this.answerTableData.filter(station => this.isStationComplete(station)).length
    },

    // 动态计算完成进度
    dynamicCompletionPercentage() {
      if (this.stationList.length === 0) return 0
      var completed = this.stationList.filter(station => this.isStationCompleteNew(station)).length
      return Math.round((completed / this.stationList.length) * 100)
    },

    // 动态已完成站点数
    dynamicCompletedStations() {
      return this.stationList.filter(station => this.isStationCompleteNew(station)).length
    },

    // 是否可以提交整合表单
    canSubmit() {
      // 基本信息必须完整（移除站点数量要求，允许没有站点也能保存）
      var basicComplete = this.questionForm.title &&
                           this.questionForm.content &&
                           this.questionForm.forecastDate &&
                           this.questionForm.forecastTime

      // 如果有站点，答案也必须完整
      var answerComplete = this.stationList.length === 0 || this.dynamicCompletionPercentage === 100

      return basicComplete && answerComplete
    }
  },
  watch: {
    // 监听站点列表变化，自动生成答案数据
    stationList: {
      handler(newStations) {
        this.generateAnswerData(newStations)
      },
      deep: true
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      const params = {
        current: this.listQuery.page,
        size: this.listQuery.limit,
        params: {
          content: this.listQuery.title,
          quType: 6 // 天气预报表格题
        }
      }

      getWeatherCaseList(params).then(response => {
        this.questionList = response.data.records || []
        this.total = response.data.total || 0
        this.loading = false
      }).catch(error => {
        console.error('获取题目列表失败:', error)
        this.$message.error('获取题目列表失败')
        this.loading = false
      })
    },

    resetQuery() {
      this.listQuery = {
        page: 1,
        limit: 10,
        title: '',
        forecastDate: ''
      }
      this.getList()
    },

    getStationCount(row) {
      try {
        if (row.scenarioData) {
          const scenarioData = JSON.parse(row.scenarioData)
          return (scenarioData.stations || []).length
        }
        // 兼容旧数据格式
        return JSON.parse(row.stations || '[]').length
      } catch (e) {
        return 0
      }
    },

    getDataFileName(row) {
      try {
        if (row.scenarioData) {
          const scenarioData = JSON.parse(row.scenarioData)
          return scenarioData.dataFileName || null
        }
        return row.dataFileName || null
      } catch (e) {
        return null
      }
    },

    getPrecipitationAreaFileName(row) {
      try {
        if (row.scenarioData) {
          const scenarioData = JSON.parse(row.scenarioData)
          return scenarioData.observationFileName || null
        }
        return row.observationFileName || null
      } catch (e) {
        return null
      }
    },

    hasStandardAnswer(row) {
      try {
        if (row.scenarioData) {
          const scenarioData = JSON.parse(row.scenarioData)
          const answers = scenarioData.answers || {}
          const stations = scenarioData.stations || []

          // 检查是否所有站点都有完整的答案
          for (const station of stations) {
            const stationAnswer = answers[station]
            if (!stationAnswer ||
                !stationAnswer.windForce ||
                !stationAnswer.windDirection ||
                stationAnswer.minTemperature === null ||
                stationAnswer.maxTemperature === null ||
                !stationAnswer.precipitation ||
                !stationAnswer.disasterWeather || stationAnswer.disasterWeather.length === 0) {
              return false
            }
          }
          return stations.length > 0
        }
        return false
      } catch (e) {
        return false
      }
    },

    formatDate(date) {
      if (!date) return ''
      if (typeof date === 'string' && date.includes('-')) {
        return date
      }
      return new Date(date).toISOString().split('T')[0]
    },

    handleAdd() {
      this.dialogTitle = '新增历史个例题目'
      this.isEdit = false
      this.dialogVisible = true

      this.resetForm()
    },

    handleEdit(row) {
      this.dialogTitle = '编辑历史个例题目'
      this.isEdit = true

      // 先重置表单，然后只复制基本字段，避免覆盖从scenarioData解析的字段
      this.resetForm()
      this.questionForm.id = row.id
      this.questionForm.title = row.title
      this.questionForm.content = row.content
      this.questionForm.totalScore = row.totalScore
      this.questionForm.analysis = row.analysis
      this.questionForm.remark = row.remark
      this.questionForm.level = row.level

      // 优先从根级别获取forecastDate和forecastTime（新格式）
      if (row.forecastDate) {
        this.questionForm.forecastDate = row.forecastDate
      }
      if (row.forecastTime) {
        this.questionForm.forecastTime = row.forecastTime
      }

      // 解析scenarioData中的历史个例数据
      try {
        if (row.scenarioData) {
          console.log('=== 编辑题目 - 解析scenarioData ===')
          const scenarioData = JSON.parse(row.scenarioData)
          this.stationList = scenarioData.stations || []

          // 如果根级别没有这些字段，从scenarioData中获取（兼容旧数据）
          if (!row.forecastDate && scenarioData.forecastDate) {
            this.questionForm.forecastDate = scenarioData.forecastDate
          }
          if (!row.forecastTime && scenarioData.forecastTime) {
            this.questionForm.forecastTime = scenarioData.forecastTime
          }

          // 第一部分：降水分级落区预报数据
          this.questionForm.precipitationContent = scenarioData.precipitationContent || ''
          this.questionForm.precipitationRegion = scenarioData.precipitationRegion || ''

          // 第二部分：灾害性天气预报数据
          this.answerData = scenarioData.answers || {}

          // 文件数据
          this.questionForm.dataFileName = scenarioData.dataFileName
          this.questionForm.dataFilePath = scenarioData.dataFilePath
          this.questionForm.observationFileName = scenarioData.observationFileName
          this.questionForm.observationFilePath = scenarioData.observationFilePath
          this.questionForm.forecastStartDate = scenarioData.forecastStartDate // 落区预报起报时间
          this.questionForm.precipitationForecastTime = scenarioData.precipitationForecastTime || '08' // 落区预报起报时次
          this.questionForm.cmaFileName = scenarioData.cmaFileName // 落区CMA文件名
          this.questionForm.cmaFilePath = scenarioData.cmaFilePath // 落区CMA文件路径

          // 重建文件列表以显示已上传的文件
          this.rebuildFileList(scenarioData)

          console.log('设置CMA文件信息:', {
            cmaFileName: this.questionForm.cmaFileName,
            cmaFilePath: this.questionForm.cmaFilePath
          })
        } else {
          // 兼容旧数据格式
          this.stationList = JSON.parse(row.stations || '[]')
        }
      } catch (e) {
        console.error('解析scenarioData失败:', e)
        this.stationList = []
      }

      this.dialogVisible = true
      // 如果有站点但没有答案数据，生成答案数据
      if (this.stationList.length > 0 && Object.keys(this.answerData).length === 0) {
        this.generateAnswerData(this.stationList)
      }
    },

    handleDelete(row) {
      this.$confirm('确定要删除这个题目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteWeatherCase({ id: row.id }).then(() => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(error => {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        })
      })
    },

    resetForm() {
      this.questionForm = {
        id: '',
        title: '',
        content: '',
        precipitationContent: '', // 第一部分：降水预报题目内容
        precipitationRegion: '', // 第一部分：预报区域选择
        forecastDate: null,
        forecastTime: '08',
        forecastStartDate: null, // 落区预报起报时间
        precipitationForecastTime: '08', // 落区预报起报时次
        dataFilePath: '',
        dataFileName: '',
        observationFilePath: '', // 降水落区实况文件路径
        observationFileName: '', // 降水落区实况文件名
        cmaFilePath: '', // 落区CMA文件路径
        cmaFileName: '', // 落区CMA文件名
        totalScore: 0, // 历史天气个例题目总分由系统自动计算
        analysis: '',
        remark: '',
        level: 1
      }
      // 第二部分：灾害性天气预报数据重置
      this.stationList = []
      this.answerTableData = []
      this.answerData = {}
      this.answerRowData = {}
      this.inputVisible = false
      this.inputValue = ''

      // 清空文件列表
      this.dataFileList = []
      this.precipitationAreaFileList = []
      this.cmaFileList = []

      if (this.$refs.questionForm) {
        this.$refs.questionForm.resetFields()
      }
    },

    showInput() {
      this.inputVisible = true
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    handleInputConfirm() {
      var inputValue = this.inputValue
      if (inputValue && !this.stationList.includes(inputValue)) {
        this.stationList.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    },

    removeStation(index) {
      this.stationList.splice(index, 1)
    },

    beforeUpload(file) {
      var isValidType = file.name.endsWith('.dat') ||
                       file.name.endsWith('.txt') ||
                       file.name.endsWith('.zip') ||
                       file.name.endsWith('.rar') ||
                       file.name.endsWith('.7z') ||
                       file.name.endsWith('.tar') ||
                       file.name.endsWith('.gz') ||
                       file.name.endsWith('.bz2')
      if (!isValidType) {
        this.$message.error('只能上传 .dat, .txt, .zip, .rar, .7z, .tar, .gz, .bz2 格式的文件!')
        return false
      }
      var isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('Micaps数据文件大小不能超过 50MB!')
        return false
      }
      return true
    },

    handleUploadSuccess(response, file) {
      if (response.code === 0) {
        // 使用专用接口返回的文件路径
        const filePath = response.data.filePath
        const fileName = response.data.name

        this.questionForm.dataFilePath = filePath
        this.questionForm.dataFileName = fileName

        // 更新文件列表
        this.dataFileList = [{
          uid: file.uid,
          name: fileName,
          size: file.size,
          response: response
        }]

        this.$message.success('个例数据文件上传成功')
      } else {
        this.$message.error(response.message || '文件上传失败')
      }
    },

    handleUploadError() {
      this.$message.error('文件上传失败')
    },

    // 个例数据文件移除回调
    handleDataFileRemove(file, fileList) {
      this.dataFileList = fileList
      this.questionForm.dataFileName = ''
      this.questionForm.dataFilePath = ''
    },

    // 降水落区实况文件上传前检查
    beforePrecipitationAreaUpload(file) {
      const isValidFormat = /\.(dat|txt|000|024|csv)$/i.test(file.name)
      if (!isValidFormat) {
        this.$message.error('只能上传 .dat, .txt, .000, .024, .csv 格式的文件!')
        return false
      }
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('文件大小不能超过 50MB!')
        return false
      }
      return true
    },

    // 降水落区实况文件上传成功
    handlePrecipitationAreaUploadSuccess(response, file) {
      if (response.code === 0) {
        // 使用专用接口返回的文件路径
        const filePath = response.data.filePath
        const fileName = response.data.name

        this.questionForm.observationFilePath = filePath
        this.questionForm.observationFileName = fileName

        // 更新文件列表
        this.precipitationAreaFileList = [{
          uid: file.uid,
          name: fileName,
          size: file.size,
          response: response
        }]

        this.$message.success('降水落区实况文件上传成功')
      } else {
        this.$message.error(response.message || '降水落区实况文件上传失败')
      }
    },

    // 降水落区实况文件上传失败
    handlePrecipitationAreaUploadError() {
      this.$message.error('降水落区实况文件上传失败')
    },

    // 降水落区实况文件移除回调
    handlePrecipitationAreaFileRemove(file, fileList) {
      this.precipitationAreaFileList = fileList
      this.questionForm.observationFileName = ''
      this.questionForm.observationFilePath = ''
    },

    // 实况文件上传前验证
    beforeObservationUpload(file) {
      var isValidType = file.name.endsWith('.dat') ||
                       file.name.endsWith('.txt') ||
                       file.name.endsWith('.nc') ||
                       file.name.endsWith('.000') ||
                       file.name.endsWith('.024')
      if (!isValidType) {
        this.$message.error('只能上传 .dat, .txt, .nc, .000, .024 格式的文件!')
        return false
      }
      var isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('实况文件大小不能超过 50MB!')
        return false
      }
      return true
    },

    // CMA文件上传前检查
    beforeCmaUpload(file) {
      const isValidFormat = /\.(cma|dat|txt|024)$/i.test(file.name)
      if (!isValidFormat) {
        this.$message.error('只能上传 .cma, .dat, .txt, .024 格式的文件!')
        return false
      }
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('文件大小不能超过 50MB!')
        return false
      }
      return true
    },

    // CMA文件上传成功
    handleCmaUploadSuccess(response, file) {
      if (response.code === 0) {
        // 使用专用接口返回的文件路径
        const filePath = response.data.filePath
        const fileName = response.data.name

        this.questionForm.cmaFilePath = filePath
        this.questionForm.cmaFileName = fileName

        // 更新文件列表
        this.cmaFileList = [{
          uid: file.uid,
          name: fileName,
          size: file.size,
          response: response
        }]

        this.$message.success('CMA-MESO文件上传成功')
      } else {
        this.$message.error(response.message || 'CMA文件上传失败')
      }
    },

    // CMA文件上传失败
    handleCmaUploadError() {
      this.$message.error('CMA文件上传失败')
    },

    // 落区CMA文件移除回调
    handleCmaFileRemove(file, fileList) {
      this.cmaFileList = fileList
      this.questionForm.cmaFileName = ''
      this.questionForm.cmaFilePath = ''
    },

    // 生成动态答案数据
    generateAnswerData(stations) {
      // 为每个站点初始化答案数据
      stations.forEach(station => {
        if (!this.answerData[station]) {
          this.$set(this.answerData, station, {
            windForce: '',
            windDirection: '',
            minTemperature: null,
            maxTemperature: null,
            precipitation: '',
            disasterWeather: []
          })
        }
      })

      // 移除不存在的站点数据
      var currentStations = stations.slice()
      Object.keys(this.answerData).forEach(station => {
        if (!currentStations.includes(station)) {
          this.$delete(this.answerData, station)
        }
      })
    },

    // 获取时间标签（根据起报时次动态生成）
    getTimeLabel() {
      var time = this.questionForm.forecastTime || '08'
      return time + '-' + time
    },

    // 检查站点是否完成（新版本）
    isStationCompleteNew(station) {
      var data = this.answerData[station]
      if (!data) return false

      return data.windForce &&
             data.windDirection &&
             data.minTemperature !== null &&
             data.maxTemperature !== null &&
             data.precipitation &&
             data.disasterWeather && data.disasterWeather.length > 0
    },

    // 提交整合表单
    submitIntegratedForm() {
      this.$refs.questionForm.validate((valid) => {
        if (valid) {
          // 移除站点数量的强制要求，允许没有站点也能保存
          // if (this.stationList.length === 0) {
          //   this.$message.error('请至少添加一个站点')
          //   return
          // }

          // 如果有站点，则要求完成所有站点的答案设置
          if (this.stationList.length > 0 && this.dynamicCompletionPercentage < 100) {
            this.$message.error('请完成所有站点的答案设置')
            return
          }

          // 准备历史个例特殊数据（不包含forecastDate和forecastTime，它们现在是根级别字段）
          const scenarioData = {
            // 第一部分：降水分级落区预报数据
            precipitationContent: this.questionForm.precipitationContent,
            precipitationRegion: this.questionForm.precipitationRegion,

            // 第二部分：灾害性天气预报数据
            stations: this.stationList,
            answers: this.answerData,

            // 文件数据
            dataFileName: this.questionForm.dataFileName,
            dataFilePath: this.questionForm.dataFilePath,
            observationFileName: this.questionForm.observationFileName,
            observationFilePath: this.questionForm.observationFilePath,
            forecastStartDate: this.questionForm.forecastStartDate, // 落区预报起报时间
            precipitationForecastTime: this.questionForm.precipitationForecastTime, // 落区预报起报时次
            cmaFileName: this.questionForm.cmaFileName, // 落区CMA文件名
            cmaFilePath: this.questionForm.cmaFilePath // 落区CMA文件路径
          }

          console.log('=== submitIntegratedForm - CMA文件信息 ===')
          console.log('CMA文件名:', this.questionForm.cmaFileName)
          console.log('CMA文件路径:', this.questionForm.cmaFilePath)

          // 自动计算总分：
          // 第一部分（降水预报）：固定40分
          // 第二部分（天气预报）：每个站点10分（风力1分+风向1分+最低温2分+最高温2分+降水2分+灾害天气2分）
          const precipitationScore = 40
          const weatherScore = this.stationList.length * 10
          const autoTotalScore = precipitationScore + weatherScore

          // 准备提交数据
          var formData = Object.assign({}, this.questionForm, {
            quType: 6, // 天气预报表格题
            repoIds: ['weather_forecast_repo_001'], // 天气预报历史个例题库
            weatherConfigId: '1000000000000000001', // 默认天气预报表格配置
            tableConfigId: '1000000000000000001', // 表格配置ID
            totalScore: autoTotalScore, // 自动计算的总分
            scenarioData: JSON.stringify(scenarioData), // 序列化历史个例数据
            answerList: [] // 空的答案列表，因为天气预报题不使用传统的选择题答案
          })

          // 调用API保存数据
          saveWeatherCase(formData).then(() => {
            this.$message.success(this.isEdit ? '修改成功' : '新增成功')
            this.dialogVisible = false
            this.getList()
          }).catch(error => {
            console.error('保存失败:', error)
            this.$message.error('保存失败')
          })
        }
      })
    },

    submitForm() {
      this.$refs.questionForm.validate((valid) => {
        if (valid) {
          // 准备历史个例特殊数据（不包含forecastDate和forecastTime，它们现在是根级别字段）
          const scenarioData = {
            stations: this.stationList,
            dataFileName: this.questionForm.dataFileName,
            dataFilePath: this.questionForm.dataFilePath,
            observationFileName: this.questionForm.observationFileName,
            observationFilePath: this.questionForm.observationFilePath,
            forecastStartDate: this.questionForm.forecastStartDate, // 落区预报起报时间
            precipitationForecastTime: this.questionForm.precipitationForecastTime, // 落区预报起报时次
            cmaFileName: this.questionForm.cmaFileName, // 落区CMA文件名
            cmaFilePath: this.questionForm.cmaFilePath, // 落区CMA文件路径
            answers: this.answerData // 包含答案数据
          }

          console.log('=== 保存题目 - CMA文件信息 ===')
          console.log('CMA文件名:', this.questionForm.cmaFileName)
          console.log('CMA文件路径:', this.questionForm.cmaFilePath)

          // 准备提交数据
          const formData = Object.assign({}, this.questionForm, {
            quType: 6, // 天气预报表格题
            repoIds: ['weather_forecast_repo_001'], // 天气预报历史个例题库
            weatherConfigId: '1000000000000000001', // 默认天气预报表格配置
            tableConfigId: '1000000000000000001', // 表格配置ID
            scenarioData: JSON.stringify(scenarioData), // 序列化历史个例数据
            answerList: [] // 空的答案列表，因为天气预报题不使用传统的选择题答案
          })

          // 调用后端API保存数据
          saveWeatherCase(formData).then(() => {
            this.$message.success(this.isEdit ? '修改成功' : '新增成功')
            this.dialogVisible = false
            this.getList()
          }).catch(error => {
            console.error('保存失败:', error)
            this.$message.error('保存失败')
          })
        }
      })
    },

    // 检查站点是否完成
    isStationComplete(station) {
      return station.windForce &&
             station.windDirection &&
             station.minTemperature !== null &&
             station.maxTemperature !== null &&
             station.precipitation &&
             station.disasterWeather && station.disasterWeather.length > 0
    },

    // 获取进度条颜色
    getProgressColor(percentage) {
      if (percentage === 100) return '#67c23a'
      if (percentage >= 80) return '#409eff'
      if (percentage >= 50) return '#e6a23c'
      return '#f56c6c'
    },

    // 预览答案
    previewAnswers() {
      var incompleteStations = this.stationList.filter(station => !this.isStationCompleteNew(station))
      if (incompleteStations.length > 0) {
        this.$message.warning('还有 ' + incompleteStations.length + ' 个站点未完成设置')
        return
      }

      // 显示答案预览
      var timeLabel = this.getTimeLabel()
      var previewContent = '<h3>' + this.questionForm.title + '</h3>'
      previewContent += '<p>起报时间：' + this.questionForm.forecastDate + ' ' + this.questionForm.forecastTime + '时</p>'
      previewContent += '<table border="1" style="border-collapse: collapse; width: 100%; margin-top: 10px;">'
      previewContent += '<tr style="background: #f5f7fa;"><th>站点</th><th>' + timeLabel + '最大风力</th><th>' + timeLabel + '风向</th><th>最低气温</th><th>最高气温</th><th>' + timeLabel + '降水</th><th>灾害天气</th></tr>'

      this.stationList.forEach(station => {
        var data = this.answerData[station]
        previewContent += '<tr>' +
          '<td>' + station + '</td>' +
          '<td>' + data.windForce + '</td>' +
          '<td>' + data.windDirection + '</td>' +
          '<td>' + data.minTemperature + '℃</td>' +
          '<td>' + data.maxTemperature + '℃</td>' +
          '<td>' + data.precipitation + '</td>' +
          '<td>' + (Array.isArray(data.disasterWeather) ? data.disasterWeather.join('、') : data.disasterWeather) + '</td>' +
        '</tr>'
      })
      previewContent += '</table>'

      this.$alert(previewContent, '答案预览', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      })
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (size === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(size) / Math.log(k))
      return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 下载个例数据文件
    downloadDataFile(file) {
      const url = process.env.VUE_APP_BASE_API + '/upload/file/' + file.response.data.url
      window.open(url, '_blank')
    },

    // 移除个例数据文件
    removeDataFile(file) {
      this.$confirm('确定要删除此文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.dataFileList = this.dataFileList.filter(f => f.uid !== file.uid)
        this.questionForm.dataFileName = ''
        this.questionForm.dataFilePath = ''
        this.$message.success('文件删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 下载降水落区实况文件
    downloadPrecipitationAreaFile(file) {
      const url = process.env.VUE_APP_BASE_API + '/upload/file/' + file.response.data.url
      window.open(url, '_blank')
    },

    // 移除降水落区实况文件
    removePrecipitationAreaFile(file) {
      this.$confirm('确定要删除此文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.precipitationAreaFileList = this.precipitationAreaFileList.filter(f => f.uid !== file.uid)
        this.questionForm.observationFileName = ''
        this.questionForm.observationFilePath = ''
        this.$message.success('文件删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 下载落区CMA文件
    downloadCmaFile(file) {
      const url = process.env.VUE_APP_BASE_API + '/upload/file/' + file.response.data.url
      window.open(url, '_blank')
    },

    // 移除落区CMA文件
    removeCmaFile(file) {
      this.$confirm('确定要删除此文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.cmaFileList = this.cmaFileList.filter(f => f.uid !== file.uid)
        this.questionForm.cmaFileName = ''
        this.questionForm.cmaFilePath = ''
        this.$message.success('文件删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 重建文件列表（用于编辑时显示已上传的文件）
    rebuildFileList(scenarioData) {
      // 重建个例数据文件列表
      if (scenarioData.dataFileName && scenarioData.dataFilePath) {
        this.dataFileList = [{
          uid: 'edit_data_' + Date.now(),
          name: scenarioData.dataFileName,
          size: 0, // 编辑时无法获取原始文件大小
          response: {
            code: 0,
            data: {
              filePath: scenarioData.dataFilePath,
              name: scenarioData.dataFileName,
              url: scenarioData.dataFilePath.replace(process.env.VUE_APP_BASE_API + '/upload/file/', '')
            }
          }
        }]
      } else {
        this.dataFileList = []
      }

      // 重建降水落区实况文件列表
      if (scenarioData.observationFileName && scenarioData.observationFilePath) {
        this.precipitationAreaFileList = [{
          uid: 'edit_precipitation_area_' + Date.now(),
          name: scenarioData.observationFileName,
          size: 0, // 编辑时无法获取原始文件大小
          response: {
            code: 0,
            data: {
              filePath: scenarioData.observationFilePath,
              name: scenarioData.observationFileName,
              url: scenarioData.observationFilePath.replace(process.env.VUE_APP_BASE_API + '/upload/file/', '')
            }
          }
        }]
      } else {
        this.precipitationAreaFileList = []
      }

      // 重建落区CMA文件列表
      if (scenarioData.cmaFileName && scenarioData.cmaFilePath) {
        this.cmaFileList = [{
          uid: 'edit_cma_' + Date.now(),
          name: scenarioData.cmaFileName,
          size: 0, // 编辑时无法获取原始文件大小
          response: {
            code: 0,
            data: {
              filePath: scenarioData.cmaFilePath,
              name: scenarioData.cmaFileName,
              url: scenarioData.cmaFilePath.replace(process.env.VUE_APP_BASE_API + '/upload/file/', '')
            }
          }
        }]
      } else {
        this.cmaFileList = []
      }

      console.log('重建文件列表完成:', {
        dataFileList: this.dataFileList,
        precipitationAreaFileList: this.precipitationAreaFileList,
        cmaFileList: this.cmaFileList
      })
    }
  }
}
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.upload-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

.answer-header {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.answer-header h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.answer-header p {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.forecast-info {
  font-size: 14px;
  color: #409eff;
  font-weight: 500;
}

/* 答案设置弹窗样式 */
.answer-header {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.question-info h3 {
  margin: 0 0 10px 0;
  color: white;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.question-info h3 i {
  margin-right: 8px;
}

.question-content {
  margin: 0 0 15px 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.6;
}

.forecast-meta {
  display: flex;
  align-items: center;
}

.operation-tips {
  margin-bottom: 20px;
}

.answer-table-container {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.custom-header {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  line-height: 1.2;
}

.custom-header i {
  margin-bottom: 4px;
  font-size: 16px;
}

.custom-header small {
  color: #909399;
  font-size: 12px;
}

.temperature-range {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.temp-separator {
  margin: 0 6px;
  color: #909399;
  font-weight: bold;
  font-size: 14px;
}

/* 起报时间配置样式 */
.forecast-time-config {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.time-group {
  display: flex;
  align-items: center;
}

.time-label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
  white-space: nowrap;
}

.required-field {
  border-color: #f56c6c !important;
}

.required-field:focus {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;
}

.progress-stats {
  margin-top: 15px;
}

.stat-item {
  width: 100%;
}

/* 旧的progress-info样式，保留用于兼容性 */
.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.progress-detail {
  color: #909399;
  font-size: 12px;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.progress-text-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-indicators-inline {
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-tag-inline {
  white-space: nowrap;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f5f7fa !important;
}

/* 完成状态标签样式 */
.el-tag.el-tag--success {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

.el-tag.el-tag--warning {
  background-color: #fdf6ec;
  border-color: #e6a23c;
  color: #e6a23c;
}

/* 表格容器样式 */
.answer-table-container {
  margin: 20px 0;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表格内容优化 */
.answer-table-container .el-table {
  font-size: 13px;
  width: 100% !important;
}

.answer-table-container .el-table .el-table__body-wrapper {
  overflow-x: auto;
}

.answer-table-container .el-table .el-table__header-wrapper {
  overflow-x: hidden;
}

/* 确保表格列填满可用空间 */
.answer-table-container .el-table__body,
.answer-table-container .el-table__header {
  width: 100% !important;
}

/* 温度范围样式 */
.temperature-range {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 5px;
}

.temp-separator {
  color: #909399;
  font-weight: bold;
  margin: 0 3px;
  font-size: 14px;
}

/* 确保温度输入框完整显示 */
.temperature-range .el-input-number {
  flex-shrink: 0;
}

.temperature-range .el-input-number .el-input__inner {
  text-align: center;
  padding-left: 5px;
  padding-right: 25px;
}

/* 进度统计区域优化 */
.progress-stats .el-card__body {
  padding: 20px;
}

.progress-stats {
  margin-top: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .progress-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .status-indicators-inline {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .temperature-range {
    flex-direction: column;
    gap: 5px;
  }

  .temperature-range .temp-separator {
    display: none;
  }
}

/* 整合对话框样式 */
.el-tabs--border-card {
  border: none;
  box-shadow: none;
}

.el-tabs--border-card > .el-tabs__content {
  padding: 0;
}

.station-config {
  min-height: 60px;
  padding: 10px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.station-tips {
  margin-top: 10px;
}

.empty-stations {
  padding: 60px 0;
  text-align: center;
}

.answer-content {
  padding: 0 20px 20px 20px;
}

.answer-tips {
  margin-bottom: 20px;
}

.integrated-answer-table {
  margin-bottom: 20px;
}

.upload-tip {
  margin-top: 8px;
  color: #67c23a;
  font-size: 12px;
}

.upload-description {
  margin-top: 8px;
}

.upload-description .el-alert {
  border-radius: 4px;
}

.upload-description .el-alert--info {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
}

/* 标准答案表格样式 */
.answer-table-section {
  margin-top: 20px;
}

.standard-answer-table {
  margin-bottom: 20px;
}

/* 必填字段样式 */
.required-field .el-input__inner,
.required-field .el-input-number__input {
  border-color: #f56c6c !important;
}

.required-field .el-input__inner:focus,
.required-field .el-input-number__input:focus {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;
}

/* 上传容器样式 */
.upload-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-upload-area {
  margin-bottom: 12px;

  ::v-deep .el-upload-dragger {
    width: 100%;
    height: 90px;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    &:hover {
      border-color: #409eff;
      background-color: #f0f9ff;
    }

    .el-icon-upload {
      font-size: 24px;
      color: #c0c4cc;
      margin-bottom: 8px;
    }

    .el-upload__text {
      color: #606266;
      font-size: 13px;
      line-height: 1.4;
      text-align: center;

      em {
        color: #409eff;
        font-style: normal;
        font-weight: bold;
      }
    }

    .el-upload__tip {
      margin-top: 6px;
      color: #909399;
      font-size: 11px;
      line-height: 1.3;
      text-align: center;
    }
  }
}

.file-list {
  h5 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 13px;
    font-weight: bold;
  }

  .file-items {
    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      margin-bottom: 6px;
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        background: #f0f9ff;
        border-color: #409eff;
      }

      .file-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        i {
          color: #67c23a;
          font-size: 14px;
        }

        .file-name {
          color: #303133;
          font-weight: 500;
          font-size: 12px;
          max-width: 160px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          color: #909399;
          font-size: 11px;
        }
      }

      .file-actions {
        display: flex;
        gap: 6px;

        .el-button {
          padding: 2px 6px;
          font-size: 11px;
        }
      }
    }
  }
}

.upload-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 12px;
}

.current-file {
  color: #67c23a;
  font-size: 13px;
  font-weight: 500;
}

.file-format-tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

/* 表格行距调整 */
.el-table .el-table__row {
  height: 48px;
}

.el-table .el-table__row td {
  padding: 8px 0;
}

.title-cell {
  line-height: 1.4;
  padding: 4px 0;
  word-break: break-word;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .file-upload-area ::v-deep .el-upload-dragger {
    height: 80px;
  }

  .file-name {
    max-width: 140px !important;
  }
}

@media (max-width: 768px) {
  .el-row .el-col {
    margin-bottom: 15px;
  }

  .file-upload-area ::v-deep .el-upload-dragger {
    height: 70px;
  }

  .file-name {
    max-width: 120px !important;
  }
}

</style>

